<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="128" height="64" viewBox="0 0 128 64" xml:space="preserve">
<desc>Created with Fabric.js 4.6.0</desc>
<defs>
</defs>
<g transform="matrix(0 -1 1 0 42 32)" id="hClga4AIY0a8rNex7AKGY"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(248,0,49); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-27.5, -27.5)" d="M 28.3041 55 L 55 27.5 L 28.30465 0 L 28.30465 17.435 L 0 17.435 L 0 37.6134 L 28.3052 37.6134 z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 92 19.87)" id="N9YiWI5Rhzkz9j6AS8QhJ"  >
<path style="stroke: rgb(166,94,56); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(248,0,49); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-22, -5.5)" d="M 0 0 L 44 0 L 44 11 L 0 11 z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 92 44.13)" id="Tyhj0M4FEcyH6_sAzyP1B"  >
<path style="stroke: rgb(166,94,56); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(248,0,49); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-22, -5.5)" d="M 0 0 L 44 0 L 44 11 L 0 11 z" stroke-linecap="round" />
</g>
</svg>