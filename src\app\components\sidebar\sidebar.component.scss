@use "../../../assets/styles/variables" as vars;

.sidebar {
  position: fixed;
  top: 0;
  width: 316px;
  height: 100vh;
  background-color: vars.$neutral-light-grey;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 160px 24px 24px;
  overflow: auto;
  transition: width 0.3s ease;

  &.collapsed {
    width: 80px;
    padding: 80px 16px 24px;
  }
}

.sidebar-toggle-btn {
  position: fixed;
  left: 296px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background-color: vars.$neutral-light-grey !important;
  border-color: vars.$neutral-light-grey !important;
  box-shadow: 0 2px 8px #eff0f7;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000;
  transition: all 0.3s ease;

  .sidebar.collapsed + & {
    left: 60px;
  }
}

.button-container {
  display: flex;
  flex-direction: column;
  width: 100%;

  &-collapsed {
    align-items: center;
  }
}

.route-button {
  width: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 12px;
  border-radius: 8px;

  &:hover {
    background-color: vars.$primary-blue-03;
  }

  &-active {
    background-color: vars.$primary-blue-02;
    border-radius: 8px;
  }

  &-collapsed {
    justify-content: center;
    padding: 8px 4px;
  }
}

.icon-box {
  width: 40px;
  height: 40px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  margin-right: 24px;

  &-collapsed {
    margin-right: 0;
  }
}

.i-paper-clip,
.i-document {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 13px;
}

.i-sidebar-open,
.i-sidebar-close {
  width: 70px;
  height: 70px;
  min-width: 70px;
  min-height: 70px;
  display: block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.hide {
  display: none;
}

.tree-container {
  width: 100%;
  margin-bottom: 12px;

  &-collapsed {
    display: flex;
    justify-content: center;
  }
}

.tree-header {
  width: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 8px;
  position: relative;

  &:hover {
    background-color: vars.$primary-blue-03;
  }

  &-collapsed {
    justify-content: center;
    padding: 8px 4px;
  }
}

.tree-title {
  margin: 0;
  flex: 1;
  color: vars.$primary-blue-01;
}

.tree-arrow {
  position: absolute;
  right: 12px;
  width: 0;
  height: 0;
  border-left: 6px solid vars.$primary-blue-01;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.2s ease;

  &-expanded {
    transform: rotate(90deg);
  }
}

::ng-deep {
  .p-button-text.p-button-secondary:not(:disabled) {
    &:hover,
    &:active {
      background: transparent !important;
    }
  }

  .p-button:focus {
    box-shadow: 0 0 0 0 !important;
  }

  .children-container {
    margin-left: 32px;
    padding: 0;
  }

  .child-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin: 4px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: vars.$primary-blue-03;
    }

    &-active {
      background-color: vars.$primary-blue-02;

      .child-text {
        color: white;
      }
    }
  }

  .child-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .child-text {
    margin: 0;
    color: vars.$primary-blue-01;
    font-weight: 400;
  }
}
