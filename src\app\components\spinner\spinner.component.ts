import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SpinnerService } from '../../services/spinner/spinner.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-spinner',
  standalone: true,
  imports: [CommonModule, ProgressSpinnerModule],
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.scss']
})
export class SpinnerComponent {
  @Input() smallLoader = false;

  private readonly spinnerService = inject(SpinnerService);

  readonly isLoading$ = this.spinnerService.spinnerStatus$.pipe(
    takeUntilDestroyed()
  );
}