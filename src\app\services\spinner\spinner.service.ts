import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {
  private readonly spinnerStatusSource = new BehaviorSubject(false);
  readonly spinnerStatus$ = this.spinnerStatusSource.asObservable();

  show(): void {
    this.spinnerStatusSource.next(true);
  }

  hide(): void {
    this.spinnerStatusSource.next(false);
  }

  setStatus(status: boolean): void {
    this.spinnerStatusSource.next(status);
  }
}
