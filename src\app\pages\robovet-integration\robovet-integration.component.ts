import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { TableComponent } from 'src/app/components/table/table.component';

@Component({
  selector: 'app-robovet-integrations',
  imports: [CommonModule, ButtonModule, TableComponent],
  templateUrl: './robovet-integration.component.html',
  styleUrl: './robovet-integration.component.scss',
})
export class RobovetIntegrationsComponent {
  constructor() {}

  newRobovetActivation() {
    // Implement the logic to create a new RoboVet activation
    console.log('Creating a new RoboVet activation...');
  }
}
