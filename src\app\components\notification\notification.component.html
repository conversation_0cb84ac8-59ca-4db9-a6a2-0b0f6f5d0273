<div class="wrapper">
  <div *ngFor="let notification of notifications; index as i"
       [ngClass]="getNotificationClass(notification.status)"
       class="col-8 offset-2 alert alert-dismissible fade show d-flex justify-content-between align-items-center txt-style notification"
       role="alert">

    <span class="d-flex align-items-center">
      <i class="bi bi-exclamation-triangle-fill danger-icon" *ngIf="notification.status !== 200"></i>
      <span class="alert-inner--text ml-2">
        {{ notification.message }}
      </span>
    </span>

    <button *ngIf="notification.type === 'action'"
            type="button"
            class="close"
            aria-label="Close"
            (click)="onErrorAlertDismiss(i)">
      <span aria-hidden="true">×</span>
    </button>

    <a *ngIf="notification.type === 'dismissible'"
       class="btn btn-outline-dark caption custom-button"
       [routerLink]="['/subscriptionUser']">
      Acquista
    </a>
  </div>
</div>