import { Routes } from '@angular/router';
import { isSignedInGuard } from './is-signed-in.guard';
import { isLoginGuard } from './is-login.guard';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent),
    canActivate: [isLoginGuard]
  },
  {
    path: '',
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
    canActivate: [isSignedInGuard],
    children: [
      {
        path: 'reports',
        loadComponent: () => import('./pages/reports/reports.component').then(m => m.ReportsComponent),
        canActivate: [isSignedInGuard]
      },
      {
        path: 'integrations/robovet',
        loadComponent: () => import('./pages/robovet-integration/robovet-integration.component').then(m => m.RobovetIntegrationsComponent),
        canActivate: [isSignedInGuard]
      },
      { path: '', redirectTo: '/reports', pathMatch: 'full' }
    ]
  },
  { path: '**', redirectTo: '/login' }
];
