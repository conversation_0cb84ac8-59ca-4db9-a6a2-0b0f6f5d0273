{"name": "laika-control-panel-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.13", "@angular/common": "^19.2.13", "@angular/compiler": "^19.2.13", "@angular/core": "^19.2.13", "@angular/forms": "^19.2.13", "@angular/platform-browser": "^19.2.13", "@angular/platform-browser-dynamic": "^19.2.13", "@angular/router": "^19.2.13", "@aws-amplify/core": "^6.12.0", "@primeng/themes": "^19.1.3", "aws-amplify": "^6.15.0", "ngx-cookie-service": "^19.1.2", "primeng": "^19.1.3", "rxjs": "^7.8.2", "tslib": "^2.8.1", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.13", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}}