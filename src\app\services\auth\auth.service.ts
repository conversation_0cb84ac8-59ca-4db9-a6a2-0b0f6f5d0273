import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, from, of, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { AppSettings } from '../../settings/app-settings';
import { Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { signIn, confirmSignUp, resetPassword, confirmResetPassword, confirmSignIn, fetchAuthSession, getCurrentUser, signOut } from 'aws-amplify/auth';
import { TokenExpirationHandlerService } from '../token-expiration-handler/token-expiration-handler.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly currentUserSubject = new BehaviorSubject<string | null>(null);
  public readonly currentUser$ = this.currentUserSubject.asObservable();

  private readonly router = inject(Router);
  private readonly cookieService = inject(CookieService);
  private readonly tokenHandler = inject(TokenExpirationHandlerService);

  constructor() {
    const currentUser = this.cookieService.get('currentUser');
    this.currentUserSubject.next(currentUser || null);
  }

  public get currentUserValue(): string | null {
    return this.currentUserSubject.value;
  }

  isLoggedIn(skipTokenCheck: boolean = false): [boolean, string | null] {
    const accessToken = this.cookieService.get('accessToken') || null;
    const hasToken = !!accessToken;

    if (!skipTokenCheck && hasToken) {
      this.tokenHandler.startTokenCheck();
    }

    return [hasToken, accessToken];
  }

  isAuthenticated(): Observable<boolean> {
    return from(fetchAuthSession()).pipe(
      map(session => {
        const token = session.tokens?.accessToken;
        if (!token) return false;
        return (token.payload.exp || 0) > Math.floor(Date.now() / 1000);
      }),
      catchError(() => of(false))
    );
  }

  logIn(username: string, password: string, isRememberMe: boolean): Observable<any> {
    return from(signIn({ username, password })).pipe(
      tap(user => {
        if (user?.nextStep?.signInStep === "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED") {
          return;
        }
        this.tokenHandler.startTokenCheck();
      }),
      switchMap((user: any) => {
        if (user?.nextStep?.signInStep === "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED") {
          return of(user);
        }
        return from(fetchAuthSession()).pipe(
          switchMap((session: any) => {
            this.setCookies(session, isRememberMe);
            return from(getCurrentUser());
          }),
          tap((userInfo: any) => {
            this.setUserCookie(userInfo.username, isRememberMe);
            this.currentUserSubject.next(userInfo.username);
            this.router.navigate(['/reports']);
          })
        );
      }),
      catchError(error => throwError(() => error))
    );
  }

  private setCookies(session: any, isRememberMe: boolean): void {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + AppSettings.COOKIE_EXPIRATION_PERIOD);

    const accessToken = session.tokens?.accessToken?.toString() || '';
    const refreshToken = session.tokens?.idToken?.toString() || '';

    if (isRememberMe) {
      this.resetCredentials();
      this.cookieService.set('rememberCurrentUser', 'true', expirationDate, '/');
      this.cookieService.set('accessToken', accessToken, expirationDate, '/');
      this.cookieService.set('refreshToken', refreshToken, expirationDate, '/');
    } else {
      this.cookieService.set('accessToken', accessToken);
      this.cookieService.set('refreshToken', refreshToken);
    }
  }

  private setUserCookie(username: string, isRememberMe: boolean): void {
    if (isRememberMe) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + AppSettings.COOKIE_EXPIRATION_PERIOD);
      this.cookieService.set('username', username, expirationDate, '/');
    } else {
      this.cookieService.set('username', username);
    }
  }

  resetCredentials(): void {
    this.cookieService.delete('rememberCurrentUser');
    this.cookieService.delete('accessToken');
    this.cookieService.delete('refreshToken');
    this.cookieService.delete('username');
    localStorage.clear();
    this.currentUserSubject.next(null);
  }

  logOut(): Observable<void> {
    return from(signOut()).pipe(
      catchError(() => of(null)),
      tap(() => {
        this.resetCredentials();
        this.tokenHandler.stopTokenCheck();
        this.router.navigate(['/login']);
      }),
      map(() => void 0)
    );
  }

  onVerify(email: string, code: string): Observable<boolean> {
    return from(confirmSignUp({ username: email, confirmationCode: code })).pipe(
      map(result => result.isSignUpComplete),
      catchError(() => of(false))
    );
  }

  forgotPassword(email: string): Observable<any> {
    return from(resetPassword({ username: email }));
  }

  forgotPasswordSubmit(email: string, code: string, newPassword: string): Observable<any> {
    return from(confirmResetPassword({ username: email, confirmationCode: code, newPassword }));
  }

  getAccessToken(): Observable<string | null> {
    const [, accessToken] = this.isLoggedIn();
    return of(accessToken);
  }

  completeNewPassword(newPassword: string): Observable<any> {
    return from(confirmSignIn({ challengeResponse: newPassword })).pipe(
      catchError(error => throwError(() => error))
    );
  }
}
