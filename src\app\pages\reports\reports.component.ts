import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardListComponent } from '../../components/card-list/card-list.component';
import { CardData } from '../../components/card-list/card-list.interface';

@Component({
  selector: 'app-reports',
  imports: [CommonModule, CardListComponent],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
  standalone: true,
})
export class ReportsComponent implements OnInit {
  cards: CardData[] = [];

  constructor() {}

  ngOnInit(): void {
    this.initializeCards();
  }

  private initializeCards(): void {
    this.cards = [
      {
        title: 'All sessions',
        buttonLabel: 'Download',
        onClick: () => this.downloadAllSessionsReport()
      },
      {
        title: 'Usage for users',
        buttonLabel: 'Download',
        onClick: () => this.downloadUsageForUsersReport()
      },
      {
        title: 'User info for invoice',
        buttonLabel: 'Download',
        onClick: () => this.downloadUserInfoForInvoiceReport()
      }
    ];
  }

  downloadAllSessionsReport() {
    // Implement the logic to download all sessions report
    console.log('Downloading all sessions report...');
  }

  downloadUsageForUsersReport() {
    // Implement the logic to download usage for users report
    console.log('Downloading usage for users report...');
  }

  downloadUserInfoForInvoiceReport() {
    // Implement the logic to download user info for invoice report
    alert('Downloading user info for invoice report...');
  }
}
