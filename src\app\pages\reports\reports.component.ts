import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccordionModule } from 'primeng/accordion';
import { DatePicker } from 'primeng/datepicker';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-reports',
  imports: [CommonModule, AccordionModule, DatePicker, FormsModule, ButtonModule],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
  standalone: true,
})
export class ReportsComponent implements OnInit {
  rangeDatesAllSessions: Date[] | undefined;
  rangeDatesUsageForUsers: Date[] | undefined;
  maxDate: Date = new Date();

  constructor() {}

  ngOnInit(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    this.maxDate = today;
  }

  downloadAllSessionsReport() {
    // Implement the logic to download all sessions report
    console.log(this.rangeDatesAllSessions);
  }

  downloadUsageForUsersReport() {
    // Implement the logic to download usage for users report
    console.log(this.rangeDatesUsageForUsers);
  }

  downloadUserInfoForInvoiceReport() {
    // Implement the logic to download user info for invoice report
    alert('Downloading user info for invoice report...');
  }
}
