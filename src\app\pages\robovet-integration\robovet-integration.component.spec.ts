import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RobovetIntegrationsComponent } from './robovet-integration.component';

describe('RobovetIntegrationsComponent', () => {
  let component: RobovetIntegrationsComponent;
  let fixture: ComponentFixture<RobovetIntegrationsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RobovetIntegrationsComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RobovetIntegrationsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
