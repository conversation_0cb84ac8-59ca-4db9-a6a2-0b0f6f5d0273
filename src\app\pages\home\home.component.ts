import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { AuthService } from 'src/app/services/auth/auth.service';
import { NavbarComponent } from 'src/app/components/navbar/navbar.component';
import { SidebarComponent } from "../../components/sidebar/sidebar.component";

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterOutlet, NavbarComponent, SidebarComponent],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent {

  user: null | Number = null;
  payingLicense: null | Number = null;
  freeTrialLicense: null | Number = null;
  expiredFreeTrialLicense: null | Number = null;
  inactiveLicense: null | Number = null;

  constructor(
    public authenticationService: AuthService,
  ) {
  }

  async ngOnInit() {}
}
