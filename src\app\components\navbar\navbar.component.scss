:host {
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    padding: 0 10px;
    margin: 0 !important;
    width: 100%;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2px;
}

.logo-navbar {
    width: 100%;
    min-width: 80px;
    max-width: 120px;
}

.control-panel-label {
    font-family: "SoleSans-Regular", sans-serif;
    font-size: 10px;
    color: #1E0E70;
    text-align: center;
    white-space: nowrap;
    margin: 0;
    line-height: 1;
}

.primary-button {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 80px;
    max-width: 120px;
}

@media (max-width: 768px) {
    .navbar {
        padding: 0 15px;
    }

    .logo-container {
        gap: 1px;
    }

    .logo-navbar {
        min-width: 100px;
        max-width: 140px;
    }

    .control-panel-label {
        font-size: 9px;
    }

    .primary-button {
        padding: 10px 18px;
        font-size: 15px;
        min-width: 90px;
        max-width: 130px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 0 10px;
        height: 60px;
    }

    .logo-container {
        gap: 1px;
    }

    .logo-navbar {
        min-width: 90px;
        max-width: 120px;
    }

    .control-panel-label {
        font-size: 8px;
    }

    .primary-button {
        padding: 8px 14px;
        font-size: 13px;
        min-width: 70px;
        max-width: 100px;
    }
}