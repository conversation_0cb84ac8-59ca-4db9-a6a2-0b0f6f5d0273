import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Notification } from '../../components/notification/notification.interface';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly notificationSource = new BehaviorSubject<Notification | null>(null);
  readonly notification$ = this.notificationSource.asObservable();

  sendNotification(notification: Notification | null): void {
    this.notificationSource.next(notification);
  }
}
