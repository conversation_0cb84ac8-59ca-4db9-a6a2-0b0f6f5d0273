@use "variables" as vars;

:root {
  --p-primary-50: #f0f0ff;
  --p-primary-100: #e0e0ff;
  --p-primary-200: #c7c7ff;
  --p-primary-300: #a5a5ff;
  --p-primary-400: #8080ff;
  --p-primary-500: vars.$primary-blue-01;
  --p-primary-600: #1a0c63;
  --p-primary-700: #160a56;
  --p-primary-800: #120849;
  --p-primary-900: #0e063c;
  --p-primary-950: #0a042f;
}

.p-progressspinner .p-progress-spinner-circle {
  stroke: vars.$primary-blue-01 !important;
  fill: none !important;
}

.p-progressspinner svg circle {
  stroke: vars.$primary-blue-01 !important;
  fill: transparent !important;
}

p-progressspinner .p-progress-spinner-circle,
p-progressspinner svg circle {
  stroke: vars.$primary-blue-01 !important;
  fill: transparent !important;
}

::ng-deep .p-progressspinner .p-progress-spinner-circle,
::ng-deep .p-progressspinner svg circle {
  stroke: vars.$primary-blue-01 !important;
  fill: transparent !important;
}

.p-button {
  background-color: vars.$primary-blue-01;
  border-color: vars.$primary-blue-01;
  color: #ffffff;
  border-radius: 7px;
  font-family: "SoleSans-SemiBold", sans-serif;

  &:hover {
    background-color: #1a0c63;
    border-color: #1a0c63;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25);
  }
}

.p-inputtext {
  background-color: transparent !important;
  border: 1px solid vars.$primary-grey !important;
  border-radius: 10px !important;
  padding: 12px 24px !important;
  height: 50px !important;
  color: #333 !important;

  &:enabled:focus {
    outline: 0 none !important;
    outline-offset: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
    border-color: vars.$primary-blue-01 !important;
    background-color: transparent !important;
  }

  &:enabled:hover {
    border-color: vars.$primary-blue-01 !important;
    background-color: transparent !important;
  }
}

.p-password {
  .p-inputtext {
    background-color: transparent !important;
    border: 1px solid vars.$primary-grey !important;
    border-radius: 10px !important;
    padding: 12px 24px !important;
    height: 50px !important;
    width: 100% !important;

    &:enabled:focus {
      outline: 0 none !important;
      outline-offset: 0 !important;
      box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
      border-color: vars.$primary-blue-01 !important;
      background-color: transparent !important;
    }

    &:enabled:hover {
      border-color: vars.$primary-blue-01 !important;
      background-color: transparent !important;
    }
  }

  .p-password-toggle-mask {
    color: vars.$primary-grey !important;

    &:hover {
      color: vars.$primary-blue-01 !important;
    }
  }
}

.p-card {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;

  .p-card-header {
    background: transparent !important;
    border: none !important;
    border-bottom: none !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    padding: 1.5rem 1.5rem 0 1.5rem !important;
    box-shadow: none !important;
    outline: none !important;
  }

  .p-card-body {
    padding: 1rem 1.5rem 1.5rem 1.5rem !important;
  }
}

.p-message {
  border-radius: 8px !important;

  &.p-message-error {
    background-color: #fef2f2 !important;
    border: 1px solid #fecaca !important;
    color: #dc2626 !important;
  }
}

.p-label {
  color: vars.$primary-blue-01;
  font-size: 15px;
  font-family: "SoleSans-SemiBold", sans-serif;
}

.p-accordionheader {
  background: vars.$neutral-light-grey !important;
  color: vars.$primary-blue-01 !important;
}

.p-accordionpanel {
  border-color: transparent !important;
}

.p-accordioncontent-content {
  background: transparent !important;
  color: #000 !important;
  border-style: solid !important;
  border-width: 0 1px 1px 1px !important;
  border-color: vars.$neutral-light-grey !important;
}

.p-datepicker-header,
.p-datepicker-panel {
  background: #5a59ac !important;
}

.p-button-text {
  color: #fff !important;
}

.p-datepicker-day-selected {
  background: vars.$primary-blue-01 !important;
  color: #fff !important;
}
