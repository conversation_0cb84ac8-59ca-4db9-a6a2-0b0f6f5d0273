@use "assets/styles/variables" as vars;
@use "assets/styles/primeng-custom-theme" as theme;

* {
  box-sizing: border-box;
}

html,
body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100%;
  width: 100%;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}

body {
  margin: 0 !important;
  padding: 0 !important;
}

app-root {
  margin: 0 !important;
  padding: 0 !important;
  display: block;
}

.container,
.container-fluid {
  margin: 0 !important;
  padding: 0 !important;
}

app-navbar {
  margin: 0 !important;
  padding: 0 !important;
  display: block;
}

@font-face {
  font-family: "SoleSans-Light";
  src: local("SoleSans-Light"), url(./assets/fonts/SoleSans/SoleSans-LightExtended.ttf)
    format("truetype");
}

@font-face {
  font-family: "SoleSans-Medium";
  src: local("SoleSans-Medium"), url(./assets/fonts/SoleSans/SoleSans-MediumExtended.ttf)
    format("truetype");
}

@font-face {
  font-family: "SoleSans-Regular";
  src: local("SoleSans-Regular"), url(./assets/fonts/SoleSans/SoleSans-RegularExtended.ttf)
    format("truetype");
}

@font-face {
  font-family: "SoleSans-SemiBold";
  src: local("SoleSans-SemiBold"), url(./assets/fonts/SoleSans/SoleSans-SemiBoldExtended.ttf)
    format("truetype");
}

h1 {
  font-size: 70px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-SemiBold";
}

h2 {
  font-size: 54px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-SemiBold";
}

h3 {
  font-size: 34px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-SemiBold";
}

h4 {
  font-size: 24px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-SemiBold";
}

h5 {
  font-size: 20px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-Regular";
}

h6 {
  font-size: 19px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-SemiBold";
}

.primary-button {
  background-color: vars.$primary-blue-01;
  color: #ffffff;
  border-radius: 7px;
  border: none;
  padding: 12px 24px;
  cursor: pointer;
  font-family: "SoleSans-SemiBold";
}

label {
  margin-bottom: 0.25rem !important;
  color: vars.$primary-blue-01 !important;
  font-size: 15px;
  font-family: "SoleSans-SemiBold";
  float: left;
}

.input {
  border: 1px solid vars.$primary-grey;
  border-radius: 10px;
  padding: 12px 24px;
  height: 50px;
  width: 100%;
  outline: none;
  appearance: none;
}

.password-input {
  border: none;
  padding: 0;
  width: 90%;
  outline: none;
  appearance: none;
}

.password-box-input {
  border: 1px solid vars.$primary-grey;
  border-radius: 10px;
  padding: 12px 24px;
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pointer {
  cursor: pointer;
}

input:focus {
  outline: none;
}

.ui-p-light {
  font-size: 16px;
  color: vars.$primary-blue-01;
  font-family: "SoleSans-Regular";
  margin: 0;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
}

.i-paper-clip {
  background-image: url("assets/ui/icons/paper-clip.svg");
}

.i-document {
  background-image: url("assets/ui/icons/document.svg");
}

.i-sidebar-open {
  background-image: url("assets/ui/icons/sidebar-open.svg");
}

.i-sidebar-close {
  background-image: url("assets/ui/icons/sidebar-close.svg");
}
