import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './services/auth/auth.service';
import { map, catchError, of, switchMap } from 'rxjs';

export const isSignedInGuard = () => {
  const router = inject(Router);
  const authService = inject(AuthService);

  const [isLoggedIn] = authService.isLoggedIn(true);

  if (!isLoggedIn) {
    router.navigate(['/login']);
    return false;
  }

  return authService.isAuthenticated().pipe(
    switchMap(isAuthenticated => {
      if (isAuthenticated) {
        return of(true);
      }
      return authService.logOut().pipe(map(() => false));
    }),
    catchError(() => authService.logOut().pipe(map(() => false)))
  );
};
