import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'app-table',
  imports: [TableModule],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss'
})
export class TableComponent {
  products: any[] = [
    { code: 'p1', name: 'Product 1', category: 'Category 1', quantity: 10 },
    { code: 'p2', name: 'Product 2', category: 'Category 2', quantity: 20 },
    { code: 'p3', name: 'Product 3', category: 'Category 3', quantity: 30 },
    { code: 'p4', name: 'Product 4', category: 'Category 4', quantity: 40 },
    { code: 'p5', name: 'Product 5', category: 'Category 5', quantity: 50 },
    { code: 'p6', name: 'Product 6', category: 'Category 6', quantity: 60 },
    { code: 'p7', name: 'Product 7', category: 'Category 7', quantity: 70 },
    { code: 'p8', name: 'Product 8', category: 'Category 8', quantity: 80 },
    { code: 'p9', name: 'Product 9', category: 'Category 9', quantity: 90 },
    { code: 'p10', name: 'Product 10', category: 'Category 10', quantity: 100 },
    { code: 'p11', name: 'Product 11', category: 'Category 11', quantity: 110 },
    { code: 'p12', name: 'Product 12', category: 'Category 12', quantity: 120 },
    { code: 'p13', name: 'Product 13', category: 'Category 13', quantity: 130 },
    { code: 'p14', name: 'Product 14', category: 'Category 14', quantity: 140 },
    { code: 'p15', name: 'Product 15', category: 'Category 15', quantity: 150 },
    { code: 'p16', name: 'Product 16', category: 'Category 16', quantity: 160 },
    { code: 'p17', name: 'Product 17', category: 'Category 17', quantity: 170 },
    { code: 'p18', name: 'Product 18', category: 'Category 18', quantity: 180 },
    { code: 'p19', name: 'Product 19', category: 'Category 19', quantity: 190 },
    { code: 'p20', name: 'Product 20', category: 'Category 20', quantity: 200 },
  ];
}
