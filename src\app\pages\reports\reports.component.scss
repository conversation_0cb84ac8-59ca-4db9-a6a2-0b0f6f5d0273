@use "../../../assets/styles/variables" as vars;

.component-container {
  padding: 2rem;
}

.content-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.last-content-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 2.5rem;
}

.content-text {
  padding-top: 1.125rem;
}

::ng-deep {
  .content-button .p-button {
    background-color: vars.$primary-blue-01 !important;
    font-family: "SoleSans-SemiBold", sans-serif !important;
    color: #fff !important;
    border-color: vars.$primary-blue-01 !important;

    &:hover {
      background-color: #5a59ac !important;
    }
  }

  .p-inputtext {
    background-color: transparent !important;
    border: 1px solid #acacac !important;
    border-radius: 10px !important;
    padding: 0px 24px !important;
    height: 40px !important;
    color: #333 !important;
  }
}
