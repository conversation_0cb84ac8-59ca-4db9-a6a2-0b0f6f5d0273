import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { of } from 'rxjs';
import { finalize, catchError } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { AuthService } from 'src/app/services/auth/auth.service';
import { SpinnerService } from 'src/app/services/spinner/spinner.service';
import { NavbarComponent } from 'src/app/components/navbar/navbar.component';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { ButtonModule } from 'primeng/button';
import { MessageModule } from 'primeng/message';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NavbarComponent,
    CardModule,
    InputTextModule,
    PasswordModule,
    ButtonModule,
    MessageModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  signinForm!: FormGroup;
  verificationForm!: FormGroup;
  logInError = false;
  verificationCodeFlag = false;
  cognitoUser: any = null;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private spinnerService: SpinnerService,
  ) {}

  ngOnInit(): void {
    this.spinnerService.hide();

    this.signinForm = this.fb.group({
      username: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
    });

    this.verificationForm = this.fb.group({
      newPassword: ['', Validators.required],
      repeatNewPassword: ['', Validators.required],
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.signinForm.get(fieldName);
    return !!(field?.invalid && field?.touched);
  }

  isVerificationFieldInvalid(fieldName: string): boolean {
    const field = this.verificationForm.get(fieldName);
    return !!(field?.invalid && field?.touched);
  }

  onSubmit(): void {
    if (this.signinForm.invalid) {
      this.signinForm.markAllAsTouched();
      return;
    }

    this.spinnerService.show();
    this.logInError = false;

    const { username, password } = this.signinForm.value;

    this.authService.logIn(username, password, false)
      .pipe(
        finalize(() => this.spinnerService.hide()),
        catchError(() => {
          this.logInError = true;
          return of(null);
        }),
      )
      .subscribe(user => {
        if (user?.nextStep?.signInStep === 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED') {
          this.verificationCodeFlag = true;
          this.cognitoUser = user;
          return;
        }

        if (user?.message?.toLowerCase().includes('not') &&
            user?.message?.toLowerCase().includes('confirmed')) {
          this.verificationCodeFlag = true;
          return;
        }

        const [isLoggedIn] = this.authService.isLoggedIn(true);
        if (!isLoggedIn) {
          this.logInError = true;
        }
      });
  }

  onVerification(): void {
    if (this.verificationForm.invalid) {
      this.verificationForm.markAllAsTouched();
      return;
    }

    const { newPassword, repeatNewPassword } = this.verificationForm.value;

    if (newPassword === repeatNewPassword) {
      this.authService.completeNewPassword(newPassword)
        .pipe(
          catchError(() => of(null)),
          takeUntilDestroyed()
        )
        .subscribe(() => {
          this.verificationCodeFlag = false;
        });
    }
  }
}
