import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { NotificationService } from '../../services/notification/notification.service';
import { Notification } from './notification.interface';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss']
})
export class NotificationComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  private subscription?: Subscription;

  constructor(private readonly notificationService: NotificationService) {}

  onAlertDismiss(): void {
    setTimeout(() => this.notificationService.sendNotification(null), 2000);
  }

  onErrorAlertDismiss(index: number): void {
    this.notifications.splice(index, 1);
  }

  ngOnInit(): void {
    this.subscription = this.notificationService.notification$.subscribe(notification => {
      if (notification) {
        const isDuplicate = this.notifications.some(existingNotification =>
          existingNotification.message === notification.message &&
          existingNotification.status === notification.status
        );

        if (!isDuplicate) {
          this.notifications.push(notification);
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getNotificationClass(status: number): string {
    if (status === 200) {
      return 'alert-success';
    } else if ([500, 404, 401, 409, 403, 400, 1000, 1001, 1002, 1003, 1004].includes(status)) {
      return 'alert-danger';
    } else if ([406].includes(status)) {
      return 'alert-warning';
    }
    return 'alert-primary';
  }
}
