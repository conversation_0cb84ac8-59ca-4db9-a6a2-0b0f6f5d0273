<app-navbar></app-navbar>

<div class="login-container">
  <p-card class="login-card">
    <ng-template pTemplate="header">
      <div class="card-header">
        <h4>{{ verificationCodeFlag ? 'Reset Password' : 'Login' }}</h4>
      </div>
    </ng-template>

    <!-- Login Form -->
    <form
      *ngIf="!verificationCodeFlag"
      [formGroup]="signinForm"
      (ngSubmit)="onSubmit()">

      <p-message
        *ngIf="logInError"
        severity="error"
        text="Login failed. Please check your credentials."
        class="mb-3">
      </p-message>

      <div class="mb-4">
        <input
          pInputText
          type="email"
          formControlName="username"
          class="w-full"
          [class.p-invalid]="isFieldInvalid('username')"
          placeholder="Email Address">
        <small *ngIf="isFieldInvalid('username')" class="p-error">
          <span *ngIf="signinForm.get('username')?.errors?.['required']">Email is required</span>
          <span *ngIf="signinForm.get('username')?.errors?.['email']">Please enter a valid email</span>
        </small>
      </div>

      <div class="mb-3">
        <p-password
          formControlName="password"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="w-full"
          [class.p-invalid]="isFieldInvalid('password')"
          placeholder="Password">
        </p-password>
        <small *ngIf="isFieldInvalid('password')" class="p-error">
          Password is required
        </small>
      </div>

      <div class="forgot-password-link mb-3">
        <a
          href="#"
          (click)="$event.preventDefault(); verificationCodeFlag = true"
          class="forgot-link">
          Forgot Password?
        </a>
      </div>

      <div class="button-container">
        <p-button
          type="submit"
          label="Login"
          class="login-button"
          [disabled]="signinForm.invalid">
        </p-button>
      </div>
    </form>

    <!-- Password Reset Form -->
    <form
      *ngIf="verificationCodeFlag"
      [formGroup]="verificationForm"
      (ngSubmit)="onVerification()">

      <div class="mb-3">
        <p-password
          formControlName="newPassword"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="w-full"
          [class.p-invalid]="isVerificationFieldInvalid('newPassword')"
          placeholder="New Password">
        </p-password>
        <small *ngIf="isVerificationFieldInvalid('newPassword')" class="p-error">
          New password is required
        </small>
      </div>

      <div class="mb-3">
        <p-password
          formControlName="repeatNewPassword"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="w-full"
          [class.p-invalid]="isVerificationFieldInvalid('repeatNewPassword')"
          placeholder="Confirm Password">
        </p-password>
        <small *ngIf="isVerificationFieldInvalid('repeatNewPassword')" class="p-error">
          Confirm password is required
        </small>
      </div>

      <div class="button-container">
        <p-button
          type="submit"
          label="Reset Password"
          class="login-button"
          [disabled]="verificationForm.invalid">
        </p-button>
      </div>
    </form>
  </p-card>
</div>