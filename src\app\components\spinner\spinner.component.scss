.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.small-spinner-overlay {
  position: relative !important;
  background-color: transparent !important;
  width: auto !important;
  height: auto !important;
  z-index: auto !important;
}

:host ::ng-deep .p-progressspinner .p-progress-spinner-circle {
  stroke: #1E0E70 !important;
  fill: transparent !important;
}

:host ::ng-deep .p-progressspinner svg circle {
  stroke: #1E0E70 !important;
  fill: transparent !important;
}
