<div class="component-container">
    <h2>Reports</h2>
    <div class="reports-container">
        <p-accordion class="accordion" [value]="['0']" [multiple]="true">
            <p-accordion-panel value="0">
                <p-accordion-header>All sessions</p-accordion-header>
                <p-accordion-content>
                    <div class="content-container">
                        <div class="content-text">
                            <p>Select range</p>
                            <p-datepicker [(ngModel)]="rangeDatesAllSessions" selectionMode="range" [readonlyInput]="true"
                                [appendTo]="'body'" dateFormat="dd/mm/yy" [maxDate]="maxDate" />
                        </div>
                        <div class="content-button">
                            <p-button label="Download" (onClick)="downloadAllSessionsReport()"></p-button>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>
            <p-accordion-panel value="1">
                <p-accordion-header>Usage for users</p-accordion-header>
                <p-accordion-content>
                    <div class="content-container">
                        <div class="content-text">
                            <p>Select range</p>
                            <p-datepicker [(ngModel)]="rangeDatesUsageForUsers" selectionMode="range" [readonlyInput]="true"
                                [appendTo]="'body'" dateFormat="dd/mm/yy" [maxDate]="maxDate" />
                        </div>
                        <div class="content-button">
                            <p-button label="Download" (onClick)="downloadUsageForUsersReport()"></p-button>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>
            <p-accordion-panel value="2">
                <p-accordion-header>User info for invoice</p-accordion-header>
                <p-accordion-content>
                    <div class="last-content-container">
                        <div class="content-button">
                            <p-button label="Download" (onClick)="downloadUserInfoForInvoiceReport()"></p-button>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>
        </p-accordion>
    </div>
</div>