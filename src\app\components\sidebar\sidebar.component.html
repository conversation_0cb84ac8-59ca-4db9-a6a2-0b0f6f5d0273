<div class="sidebar" [ngClass]="{ 'collapsed': sidebarCollapsed }">
    <div class="button-container" [ngClass]="{ 'button-container-collapsed': sidebarCollapsed}">
        @for (route of routes; track $index) {
        @if (!!route.visible) {
            @if (!route.children) {
                <!-- Route senza children - renderizza come prima -->
                <div class="route-button" [ngClass]="{ 'route-button-collapsed': sidebarCollapsed }" [routerLink]="route.path"
                    routerLinkActive="route-button-active">
                    <div class="icon-box" [ngClass]="{ 'icon-box-collapsed': sidebarCollapsed}">
                        <i class="icon" [ngClass]="route.icon"></i>
                    </div>
                    <p class="ui-p-light" [ngClass]="{ 'hide': sidebarCollapsed }" routerLinkActive="text-white">
                        {{ route.text }}
                    </p>
                </div>
            } @else {
                <!-- Route con children - renderizza con p-tree -->
                <div class="tree-container" [ngClass]="{ 'tree-container-collapsed': sidebarCollapsed }">
                    <div class="tree-header" [ngClass]="{ 'tree-header-collapsed': sidebarCollapsed }"
                         (click)="toggleTreeExpansion(route)">
                        <div class="icon-box" [ngClass]="{ 'icon-box-collapsed': sidebarCollapsed}">
                            <i class="icon" [ngClass]="route.icon"></i>
                        </div>
                        <p class="ui-p-light tree-title" [ngClass]="{ 'hide': sidebarCollapsed }">
                            {{ route.text }}
                        </p>
                        @if (!sidebarCollapsed) {
                            <i class="tree-arrow"
                               [ngClass]="{ 'tree-arrow-expanded': route.expanded }">
                            </i>
                        }
                    </div>
                    @if (!sidebarCollapsed && route.expanded) {
                        <div class="children-container">
                            @for (child of route.children; track $index) {
                                <div class="child-item" (click)="navigateToChild(child.path)"
                                     [routerLink]="child.path" routerLinkActive="child-item-active">
                                    <i class="child-icon" [ngClass]="child.icon"></i>
                                    <p class="child-text">{{ child.text }}</p>
                                </div>
                            }
                        </div>
                    }
                </div>
            }
        }
        }
    </div>
</div>

<!-- Toggle button in overlay -->
<p-button class="sidebar-toggle-btn" (click)="toggleSidebar()" [text]="true" severity="secondary">
    <i [ngClass]="sidebarCollapsed ? 'i-sidebar-close' : 'i-sidebar-open'"></i>
</p-button>