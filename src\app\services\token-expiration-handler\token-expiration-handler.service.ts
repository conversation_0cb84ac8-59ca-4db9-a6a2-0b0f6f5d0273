import { Injectable, inject, OnDestroy } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { HttpHeaders } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable, from, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AppSettings } from '../../settings/app-settings';
import { ErrorHandlingService } from '../error-handling/error-handling.service';
import { signOut } from 'aws-amplify/auth';

@Injectable({
  providedIn: 'root'
})
export class TokenExpirationHandlerService implements OnDestroy {
  private intervalId: number | null = null;
  private readonly REST_API_SERVER = AppSettings.BACKEND_SERVER;
  private readonly httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };
  private subscriptions: Subscription[] = [];

  private readonly errorHandling = inject(ErrorHandlingService);
  private readonly cookieService = inject(CookieService);
  private readonly router = inject(Router);

startTokenCheck(): void {
    if (this.intervalId === null) {
      this.intervalId = window.setInterval(() => {
        this.checkTokenValidity();
      }, 10000);
    } else {
      this.checkTokenValidity();
    }
  }

  stopTokenCheck(): void {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private checkTokenValidity(): void {
    const accessToken = this.cookieService.get('accessToken');
    const refreshToken = this.cookieService.get('refreshToken');

    if (!accessToken || !refreshToken) {
      this.logOut();
      return;
    }

    const subscription = this.verifyAndUpdateToken(accessToken, refreshToken).subscribe({
      next: (response: any) => {
        if (response.accessToken && response.accessToken !== accessToken) {
          this.cookieService.set('accessToken', response.accessToken);
        }
      },
      error: () => {
        this.logOut();
      }
    });

    this.subscriptions.push(subscription);
  }

  resetCredentials(): void {
    this.cookieService.delete('rememberCurrentUser');
    this.cookieService.delete('accessToken');
    this.cookieService.delete('refreshToken');
    this.cookieService.delete('username');
    localStorage.clear();
  }

  logOut(): void {
    const subscription = from(signOut()).pipe(
      catchError(() => from([]))
    ).subscribe(() => {
      this.stopTokenCheck();
      this.resetCredentials();
      this.router.navigate(['/login'], { replaceUrl: true });
    });

    this.subscriptions.push(subscription);
  }

  verifyAndUpdateToken(accessToken: string, refreshToken: string): Observable<any> {
    const body = { accessToken, refreshToken };
    return this.errorHandling.post(`${this.REST_API_SERVER}verify-token`, body, this.httpOptions);
  }

  ngOnDestroy(): void {
    this.stopTokenCheck();
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    this.subscriptions = [];
  }
}
