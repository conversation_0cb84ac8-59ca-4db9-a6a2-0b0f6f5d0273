import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterModule, NavigationEnd } from '@angular/router';
import { ButtonModule } from 'primeng/button'
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-sidebar',
  imports: [RouterModule, CommonModule, ButtonModule, TreeModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent implements OnInit {
  sidebarCollapsed = false;

  routes = [
    {
      icon: 'i-document',
      path: '/reports',
      text: 'Reports',
      visible: true,
    },
    {
      icon: 'i-paper-clip',
      path: '/integrations',
      text: 'Integrations',
      visible: true,
      expanded: false,
      children: [
        {
        icon: 'i-service1',
        path: '/integrations/robovet',
        text: 'RoboVet',
        visible: true
      },
      ],
    },
  ];

  constructor(public router: Router) {}

  ngOnInit() {
    // Controlla la route iniziale
    this.checkCurrentRoute();

    // Ascolta i cambiamenti di route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.checkCurrentRoute();
    });
  }

  private checkCurrentRoute() {
    const currentUrl = this.router.url;

    // Se siamo su una route che inizia con /integrations, espandi il menu
    this.routes.forEach(route => {
      if (route.children && currentUrl.startsWith('/integrations')) {
        route.expanded = true;
      }
    });
  }

  toggleSidebar() {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  toggleTreeExpansion(route: any) {
    if (route.children) {
      route.expanded = !route.expanded;
    }
  }

  getTreeNodes(route: any): TreeNode[] {
    if (!route.children) return [];

    return route.children.map((child: any) => ({
      label: child.text,
      icon: child.icon,
      data: child.path,
      leaf: true
    }));
  }

  onNodeSelect(event: any) {
    console.log('Node selected:', event);
    const childPath = event.node.data;
    console.log('Navigating to:', childPath);
    this.router.navigate([childPath]);
  }

  navigateToChild(childPath: string) {
    console.log('Direct navigation to:', childPath);
    this.router.navigate([childPath]);
  }
}
