@use '../../../assets/styles/variables' as vars;

$primary-color: vars.$primary-blue-01;
$primary-hover: #1a0c63;
$border-color: vars.$primary-grey;
$error-color: #dc3545;
$background-light: #f8f9fa;
$font-regular: "SoleSans-Regular", sans-serif;
$font-semibold: "SoleSans-SemiBold", sans-serif;
$border-radius: 10px;
$input-height: 50px;
$input-padding: 12px 24px;

.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $background-light;
  margin: 0;
  padding: 1rem;
}

.login-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  text-align: center;
  padding: 1rem 0;
  background: transparent !important;
  border-bottom: none;

  h4 {
    font-family: $font-semibold;
    color: $primary-color;
  }
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.button-container {
  padding-top: 1rem;
  text-align: center;

  .login-button {
    width: 100%;
  }
}

.forgot-password-link {
  text-align: right;
  margin-top: 0.5rem;
}

.forgot-link {
  color: $primary-color;
  font-family: $font-regular;
  font-size: 14px;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
    color: $primary-hover;
  }
}

:host ::ng-deep {
  .p-inputtext,
  .p-password .p-inputtext {
    background-color: transparent !important;
    border: 1px solid $border-color !important;
    border-radius: $border-radius !important;
    padding: $input-padding !important;
    height: $input-height !important;
    width: 100% !important;
    font-family: $font-regular !important;

    &:enabled:focus {
      outline: 0 none !important;
      outline-offset: 0 !important;
      box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
      border-color: $primary-color !important;
      background-color: transparent !important;
    }

    &:enabled:hover {
      border-color: $primary-color !important;
      background-color: transparent !important;
    }

    &::placeholder {
      color: $border-color !important;
      font-family: $font-regular !important;
      font-size: 16px !important;
    }

    &.p-invalid {
      border-color: $error-color !important;

      &:enabled:focus {
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        border-color: $error-color !important;
      }

      &:enabled:hover {
        border-color: $error-color !important;
      }
    }
  }

  .p-password-toggle-mask {
    color: $border-color !important;

    &:hover {
      color: $primary-color !important;
    }
  }

  .p-button {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    color: #ffffff !important;
    border-radius: 7px !important;
    font-family: $font-semibold !important;
    padding: $input-padding !important;
    height: $input-height !important;
    width: 100% !important;
    display: block !important;

    &:hover:not(:disabled) {
      background-color: $primary-hover !important;
      border-color: $primary-hover !important;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
    }

    &:disabled {
      background-color: $border-color !important;
      border-color: $border-color !important;
      color: #ffffff !important;
      opacity: 0.6 !important;
      cursor: not-allowed !important;
    }
  }

  .p-message {
    font-family: $font-regular !important;
  }

  .p-error {
    color: $error-color !important;
    font-family: $font-regular !important;
    font-size: 12px !important;
    margin-top: 4px !important;
    display: block !important;
  }

  h4 {
    font-family: $font-semibold !important;
    color: $primary-color !important;
    text-align: center !important;
    margin: 0 !important;
    font-size: 36px !important;
  }

  .p-card-header {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  .p-inputtext,
  .p-password,
  .p-button,
  .p-button.p-component {
    width: 100% !important;
    display: block !important;
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 0.75rem;
  }

  .login-card {
    max-width: 350px;
  }

  :host ::ng-deep h4 {
    font-size: 32px !important;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 0.5rem;
  }

  .login-card {
    max-width: 320px;
  }

  :host ::ng-deep h4 {
    font-size: 28px !important;
  }
}