@use "../../../assets/styles/variables" as vars;

.card-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 1rem 0;
}

.report-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  height: 200px;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

.card-button-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 1.5rem;
  flex-grow: 1;
}

::ng-deep {
  .report-card {
    .p-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      border: none;
      background: #ffffff;
    }

    .p-card-header {
      padding: 2rem 1.5rem 1rem 1.5rem !important;
      border-bottom: none !important;
      text-align: center;
      flex-shrink: 0;

      h3 {
        margin: 0;
        font-family: "SoleSans-SemiBold", sans-serif;
        color: vars.$primary-blue-01;
        font-size: 1.5rem;
        font-weight: 600;
      }
    }

    .p-card-content {
      padding: 0 !important;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }

    .p-card-body {
      padding: 0 !important;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }
  }

  .card-button .p-button {
    background-color: vars.$primary-blue-01 !important;
    font-family: "SoleSans-SemiBold", sans-serif !important;
    color: #fff !important;
    border-color: vars.$primary-blue-01 !important;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    min-width: 120px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(30, 14, 112, 0.2);

    &:hover {
      background-color: vars.$primary-blue-02 !important;
      border-color: vars.$primary-blue-02 !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(30, 14, 112, 0.3);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(30, 14, 112, 0.2);
    }
  }
}

// Media queries per responsività
@media (max-width: 768px) {
  .card-list-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0.5rem 0;
  }

  .report-card {
    height: 180px;
  }

  ::ng-deep {
    .report-card .p-card-header {
      padding: 1.5rem 1rem 0.75rem 1rem !important;

      h3 {
        font-size: 1.25rem;
      }
    }

    .card-button .p-button {
      padding: 0.75rem 1.5rem;
      font-size: 0.9rem;
      min-width: 100px;
    }
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .card-list-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.75rem;
  }
}

@media (min-width: 1025px) {
  .card-list-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}
