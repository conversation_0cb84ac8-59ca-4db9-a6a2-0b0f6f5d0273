@use "../../../assets/styles/variables" as vars;

.card-list-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.report-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.card-button-container {
  display: flex;
  justify-content: flex-end;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

::ng-deep {
  .report-card .p-card-header {
    padding: 0;
    border-bottom: none;
  }

  .report-card .p-card-content {
    padding: 0;
  }

  .p-card .p-card-header {
    padding: 0px !important;
    text-align: center;

    h3 {
      margin: 0;
      font-family: "SoleSans-SemiBold", sans-serif;
      color: vars.$primary-blue-01;
      font-size: 1.5rem;
      padding: .5rem;
    }
  }

  .card-button .p-button {
    background-color: vars.$primary-blue-01 !important;
    font-family: "SoleSans-SemiBold", sans-serif !important;
    color: #fff !important;
    border-color: vars.$primary-blue-01 !important;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;

    &:hover {
      background-color: vars.$primary-blue-02 !important;
      border-color: vars.$primary-blue-02 !important;
    }
  }
}
