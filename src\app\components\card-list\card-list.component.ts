import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CardData } from './card-list.interface';

@Component({
  selector: 'app-card-list',
  imports: [CommonModule, ButtonModule, CardModule],
  templateUrl: './card-list.component.html',
  styleUrl: './card-list.component.scss',
  standalone: true,
})
export class CardListComponent {
  @Input() cards: CardData[] = [];
}
