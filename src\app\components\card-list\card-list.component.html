<div class="card-list-container">
  <p-card *ngFor="let card of cards" class="report-card">
    <ng-template pTemplate="header">
      <div class="card-header">
        <h3>{{ card.title }}</h3>
      </div>
    </ng-template>
    <ng-template pTemplate="content">
      <div class="card-button-container">
        <p-button 
          [label]="card.buttonLabel" 
          (onClick)="card.onClick()"
          class="card-button">
        </p-button>
      </div>
    </ng-template>
  </p-card>
</div>
