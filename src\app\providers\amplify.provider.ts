import { APP_INITIALIZER, Provider } from '@angular/core';
import { Amplify } from 'aws-amplify';
import { AppSettings } from '../settings/app-settings';

export function initializeAmplify(): () => Promise<void> {
  return () => {
    const config = {
      Auth: {
        Cognito: {
          userPoolId: AppSettings.COGNITO.userPoolId,
          userPoolClientId: AppSettings.COGNITO.userPoolWebClientId,
        }
      }
    };

    try {
      Amplify.configure(config);
    } catch (error) {
      console.error('Error configuring Amplify:', error);
    }

    return Promise.resolve();
  };
}

export const AmplifyProvider: Provider = {
  provide: APP_INITIALIZER,
  useFactory: initializeAmplify,
  multi: true
};
