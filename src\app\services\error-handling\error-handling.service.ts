import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NotificationService } from '../notification/notification.service';
import { Notification } from '../../components/notification/notification.interface';
import { catchError, map, Observable, throwError } from 'rxjs';

interface ApiResponse<T = unknown> {
  status: number;
  message: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {

  constructor(
    private readonly httpClient: HttpClient,
    private readonly notificationService: NotificationService
  ) {}

  get<T = unknown>(path: string, options?: object): Observable<T> {
    return this.httpClient.get<ApiResponse<T>>(path, options).pipe(
      map(response => this.handleResponse(response)),
      catchError(error => this.handleError(error))
    );
  }

  post<T = unknown>(path: string, body: object, options?: object): Observable<T> {
    return this.httpClient.post<ApiResponse<T>>(path, body, options).pipe(
      map(response => this.handleResponse(response)),
      catchError(error => this.handleError(error))
    );
  }

  put<T = unknown>(path: string, body: object, options?: object): Observable<T> {
    return this.httpClient.put<ApiResponse<T>>(path, body, options).pipe(
      map(response => this.handleResponse(response)),
      catchError(error => this.handleError(error))
    );
  }

  patch<T = unknown>(path: string, body?: object, options?: object): Observable<T> {
    return this.httpClient.patch<ApiResponse<T>>(path, body, options).pipe(
      map(response => this.handleResponse(response)),
      catchError(error => this.handleError(error))
    );
  }

  delete<T = unknown>(path: string, options?: object): Observable<T> {
    return this.httpClient.delete<ApiResponse<T>>(path, options).pipe(
      map(response => this.handleResponse(response)),
      catchError(error => this.handleError(error))
    );
  }

  private handleResponse<T>(response: ApiResponse<T> | string): T {
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;

    if (parsedResponse.status === 200) {
      return parsedResponse.data;
    }

    if (parsedResponse.status !== 401) {
      this.sendNotification(parsedResponse.message, parsedResponse.status);
    }

    throw new Error(parsedResponse.message || 'Request failed');
  }

  private handleError(error: any): Observable<never> {
    return throwError(() => new Error(error.message || 'An error occurred'));
  }

  private sendNotification(message: string, status: number): void {
    const notification: Notification = { message, status, type: 'action' };
    this.notificationService.sendNotification(notification);
  }
}
